import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { phone, message, orderId } = body

    // Sparrow SMS configuration
    const sparrowConfig = {
      token: process.env.SPARROW_SMS_TOKEN || 'demo_token',
      from: 'VertexBuild',
    }

    // Format phone number for Nepal
    const formattedPhone = phone.startsWith('977') ? phone : `977${phone.replace(/^0/, '')}`

    const smsPayload = {
      token: sparrowConfig.token,
      from: sparrowConfig.from,
      to: formattedPhone,
      text: message,
    }

    // For demo purposes, we'll simulate SMS sending
    console.log('Sending SMS:', smsPayload)

    // In production, you would call Sparrow SMS API:
    // const response = await fetch('http://api.sparrowsms.com/v2/sms/', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //   },
    //   body: JSON.stringify(smsPayload),
    // })

    // For now, simulate success
    const mockResponse = {
      status: 'success',
      message: 'SMS sent successfully',
      messageId: `msg_${Date.now()}`,
    }

    return NextResponse.json(mockResponse)
  } catch (error) {
    console.error('SMS sending error:', error)
    return NextResponse.json({ error: 'Failed to send SMS' }, { status: 500 })
  }
}

// Send order status update SMS
export async function sendOrderStatusSMS(orderId: string, status: string, customerPhone: string) {
  const statusMessages = {
    CONFIRMED: 'Your order has been confirmed and is being prepared.',
    PROCESSING: 'Your order is being processed and will be shipped soon.',
    SHIPPED: 'Your order has been shipped and is on its way to you.',
    DELIVERED: 'Your order has been delivered successfully. Thank you for shopping with us!',
    CANCELLED: 'Your order has been cancelled. If you have any questions, please contact us.',
  }

  const message = `VertexBuild Order Update: Order #${orderId} - ${statusMessages[status as keyof typeof statusMessages] || 'Status updated.'} Track your order at: ${process.env.NEXT_PUBLIC_APP_URL}/track/${orderId}`

  try {
    const response = await fetch('/api/sms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: customerPhone,
        message,
        orderId,
      }),
    })

    return response.json()
  } catch (error) {
    console.error('Error sending order status SMS:', error)
    throw error
  }
}
