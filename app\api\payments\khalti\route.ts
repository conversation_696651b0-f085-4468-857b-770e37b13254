import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { amount, orderId, customerInfo } = body

    // Khalti sandbox configuration
    const khaltiConfig = {
      publicKey: process.env.KHALTI_PUBLIC_KEY || 'test_public_key_dc74e0fd57cb46cd93832aee0a390234',
      secretKey: process.env.KHALTI_SECRET_KEY || 'test_secret_key_f59e8b7d18b4499ca40f68195a846e9b',
      returnUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/khalti/callback`,
      websiteUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    }

    // Initialize Khalti payment
    const khaltiPayload = {
      return_url: khaltiConfig.returnUrl,
      website_url: khaltiConfig.websiteUrl,
      amount: amount * 100, // <PERSON><PERSON><PERSON> expects amount in paisa
      purchase_order_id: orderId,
      purchase_order_name: `Order ${orderId}`,
      customer_info: {
        name: customerInfo.name,
        email: customerInfo.email,
        phone: customerInfo.phone,
      },
    }

    const response = await fetch('https://a.khalti.com/api/v2/epayment/initiate/', {
      method: 'POST',
      headers: {
        'Authorization': `Key ${khaltiConfig.secretKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(khaltiPayload),
    })

    const data = await response.json()

    if (response.ok) {
      return NextResponse.json({
        paymentUrl: data.payment_url,
        pidx: data.pidx,
      })
    } else {
      throw new Error(data.detail || 'Khalti payment initialization failed')
    }
  } catch (error) {
    console.error('Khalti payment error:', error)
    return NextResponse.json({ error: 'Payment initialization failed' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const pidx = searchParams.get('pidx')
    const status = searchParams.get('status')

    if (status === 'Completed' && pidx) {
      // Verify payment with Khalti
      const khaltiConfig = {
        secretKey: process.env.KHALTI_SECRET_KEY || 'test_secret_key_f59e8b7d18b4499ca40f68195a846e9b',
      }

      const response = await fetch('https://a.khalti.com/api/v2/epayment/lookup/', {
        method: 'POST',
        headers: {
          'Authorization': `Key ${khaltiConfig.secretKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pidx }),
      })

      const data = await response.json()

      if (response.ok && data.status === 'Completed') {
        // Update order status to paid
        return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?orderId=${data.purchase_order_id}`)
      }
    }

    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/failed`)
  } catch (error) {
    console.error('Khalti callback error:', error)
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/failed`)
  }
}
