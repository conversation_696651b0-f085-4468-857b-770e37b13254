'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { Package, Truck, CheckCircle, Clock, MapPin } from 'lucide-react'

interface OrderStatus {
  status: string
  timestamp: string
  description: string
  location?: string
}

interface Order {
  id: string
  orderNumber: string
  status: string
  total: number
  customerName: string
  customerEmail: string
  customerPhone: string
  shippingAddress: string
  paymentMethod: string
  paymentStatus: string
  createdAt: string
  statusHistory: OrderStatus[]
}

const statusIcons = {
  PENDING: Clock,
  CONFIRMED: Package,
  PROCESSING: Package,
  SHIPPED: Truck,
  DELIVERED: CheckCircle,
}

export default function OrderTrackingPage() {
  const params = useParams()
  const orderId = params.orderId as string
  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails()
    }
  }, [orderId])

  const fetchOrderDetails = async () => {
    try {
      // Mock order data for demo
      const mockOrder: Order = {
        id: orderId,
        orderNumber: `VB${orderId}`,
        status: 'SHIPPED',
        total: 2500,
        customerName: 'Ram Sharma',
        customerEmail: '<EMAIL>',
        customerPhone: '9841234567',
        shippingAddress: 'Kathmandu, Nepal',
        paymentMethod: 'ESEWA',
        paymentStatus: 'PAID',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        statusHistory: [
          {
            status: 'PENDING',
            timestamp: new Date(Date.now() - 86400000).toISOString(),
            description: 'Order placed successfully',
            location: 'Online',
          },
          {
            status: 'CONFIRMED',
            timestamp: new Date(Date.now() - 82800000).toISOString(),
            description: 'Order confirmed and payment received',
            location: 'Kathmandu Warehouse',
          },
          {
            status: 'PROCESSING',
            timestamp: new Date(Date.now() - 79200000).toISOString(),
            description: 'Order is being prepared for shipment',
            location: 'Kathmandu Warehouse',
          },
          {
            status: 'SHIPPED',
            timestamp: new Date(Date.now() - 43200000).toISOString(),
            description: 'Order has been shipped via courier',
            location: 'In Transit',
          },
        ],
      }

      setOrder(mockOrder)
    } catch (err) {
      setError('Failed to fetch order details')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-slate-600">Loading order details...</div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">Order Not Found</h1>
          <p className="text-slate-600">The order you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white border-b border-slate-200">
        <div className="container mx-auto px-6 py-8">
          <div className="text-center">
            <h1 className="text-3xl font-extrabold gradient-brand bg-clip-text text-transparent mb-2">
              VertexBuild
            </h1>
            <h2 className="text-2xl font-bold text-slate-900">Order Tracking</h2>
            <p className="text-slate-600 mt-2">Track your order #{order.orderNumber}</p>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Order Summary */}
          <div className="bg-white rounded-2xl p-6 shadow-lg/10 border border-slate-200 mb-8">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Order Details</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Order Number:</span>
                    <span className="font-medium">{order.orderNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Total Amount:</span>
                    <span className="font-medium">Rs {order.total.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Payment Method:</span>
                    <span className="font-medium">{order.paymentMethod}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Payment Status:</span>
                    <span className={`font-medium ${order.paymentStatus === 'PAID' ? 'text-green-600' : 'text-yellow-600'}`}>
                      {order.paymentStatus}
                    </span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Delivery Information</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Customer:</span>
                    <span className="font-medium">{order.customerName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Phone:</span>
                    <span className="font-medium">{order.customerPhone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Address:</span>
                    <span className="font-medium">{order.shippingAddress}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Order Date:</span>
                    <span className="font-medium">{new Date(order.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Order Status Timeline */}
          <div className="bg-white rounded-2xl p-6 shadow-lg/10 border border-slate-200">
            <h3 className="text-lg font-semibold text-slate-900 mb-6">Order Status</h3>
            
            <div className="space-y-6">
              {order.statusHistory.map((status, index) => {
                const Icon = statusIcons[status.status as keyof typeof statusIcons] || Package
                const isCompleted = index < order.statusHistory.length
                const isCurrent = status.status === order.status
                
                return (
                  <div key={index} className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                      isCompleted 
                        ? isCurrent 
                          ? 'bg-brand-100 text-brand-600' 
                          : 'bg-green-100 text-green-600'
                        : 'bg-slate-100 text-slate-400'
                    }`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className={`text-sm font-medium ${
                          isCompleted ? 'text-slate-900' : 'text-slate-500'
                        }`}>
                          {status.status.charAt(0) + status.status.slice(1).toLowerCase()}
                        </h4>
                        <span className="text-xs text-slate-500">
                          {new Date(status.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className={`text-sm mt-1 ${
                        isCompleted ? 'text-slate-600' : 'text-slate-400'
                      }`}>
                        {status.description}
                      </p>
                      {status.location && (
                        <div className="flex items-center mt-1 text-xs text-slate-500">
                          <MapPin className="w-3 h-3 mr-1" />
                          {status.location}
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
