'use client'

import { motion } from 'framer-motion'
import { Wallet, Truck, GaugeCircle } from 'lucide-react'
import { Player } from '@lottiefiles/react-lottie-player'
// import { SignInButton, SignUpButton, UserButton, useUser } from '@clerk/nextjs'
import Link from 'next/link'

const features = [
  {
    icon: Wallet,
    title: 'Local Payments',
    description: 'Accept payments through eSewa and Khalti with seamless integration for Nepali customers.'
  },
  {
    icon: Truck,
    title: 'Same-day Delivery',
    description: 'Connect with local delivery partners for quick pickup and same-day delivery across Nepal.'
  },
  {
    icon: GaugeCircle,
    title: 'Lightning Fast',
    description: 'Get your store online in minutes with our optimized platform built for the Nepali market.'
  }
]

export default function HomePage() {
  // const { isSignedIn } = useUser()
  const isSignedIn = false // Demo mode

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="container mx-auto px-6 py-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-extrabold gradient-brand bg-clip-text text-transparent">
            VertexBuild
          </h1>
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-slate-600 hover:text-slate-900 transition-colors">
              Features
            </a>
            <a href="#pricing" className="text-slate-600 hover:text-slate-900 transition-colors">
              Pricing
            </a>
            <a href="#contact" className="text-slate-600 hover:text-slate-900 transition-colors">
              Contact
            </a>
            {isSignedIn ? (
              <div className="flex items-center space-x-4">
                <Link href="/dashboard" className="text-slate-600 hover:text-slate-900 transition-colors">
                  Dashboard
                </Link>
                {/* <UserButton afterSignOutUrl="/" /> */}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link href="/dashboard" className="text-slate-600 hover:text-slate-900 transition-colors">
                  Dashboard (Demo)
                </Link>
                <Link href="/store" className="px-4 py-2 gradient-brand text-white font-medium rounded-lg hover:opacity-90 transition-opacity">
                  View Store
                </Link>
              </div>
            )}
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-6 py-20">
        <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <h2 className="text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
              Launch your{' '}
              <span className="gradient-brand bg-clip-text text-transparent">
                Nepali store
              </span>{' '}
              in minutes.
            </h2>
            
            <p className="text-xl text-slate-600 max-w-lg">
              Accept eSewa & Khalti payments with same-day delivery pickups across Nepal.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              {isSignedIn ? (
                <Link href="/dashboard" className="px-8 py-4 gradient-brand text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-200 inline-block text-center">
                  Go to Dashboard
                </Link>
              ) : (
                <Link href="/dashboard" className="px-8 py-4 gradient-brand text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-200 inline-block text-center">
                  Get Started Free (Demo)
                </Link>
              )}
              <button className="px-8 py-4 border-2 border-slate-300 text-slate-700 font-semibold rounded-2xl hover:border-slate-400 hover:-translate-y-1 transition-all duration-200">
                Book a Demo
              </button>
            </motion.div>
          </motion.div>

          {/* Lottie Animation */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="flex justify-center"
          >
            <Player
              autoplay
              loop
              src="https://lottie.host/4d5c9b7e-8c5e-4a1a-9b2c-3d4e5f6a7b8c/9KjHbNpQqR.json"
              style={{ height: '400px', width: '400px' }}
            />
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section id="features" className="container mx-auto px-6 py-20">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h3 className="text-3xl font-bold text-slate-900 mb-4">
              Built for Nepal
            </h3>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Everything you need to succeed in the Nepali e-commerce market
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.15 }}
                viewport={{ once: true }}
                className="bg-white p-8 rounded-2xl shadow-lg/10 hover:shadow-xl hover:-translate-y-1 transition-all duration-200"
              >
                <div className="w-12 h-12 gradient-brand rounded-xl flex items-center justify-center mb-6">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-xl font-semibold text-slate-900 mb-3">
                  {feature.title}
                </h4>
                <p className="text-slate-600 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-slate-200">
        <div className="container mx-auto px-6 py-12">
          <div className="max-w-7xl mx-auto grid md:grid-cols-4 gap-8">
            <div>
              <h5 className="text-2xl font-extrabold gradient-brand bg-clip-text text-transparent mb-4">
                VertexBuild
              </h5>
              <p className="text-slate-600">
                Empowering Nepali businesses with modern e-commerce solutions.
              </p>
            </div>
            
            <div>
              <h6 className="font-semibold text-slate-900 mb-4">Product</h6>
              <ul className="space-y-2 text-slate-600">
                <li><a href="#" className="hover:text-slate-900 transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">Templates</a></li>
              </ul>
            </div>
            
            <div>
              <h6 className="font-semibold text-slate-900 mb-4">Support</h6>
              <ul className="space-y-2 text-slate-600">
                <li><a href="#" className="hover:text-slate-900 transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">Contact</a></li>
              </ul>
            </div>
            
            <div>
              <h6 className="font-semibold text-slate-900 mb-4">Connect</h6>
              <ul className="space-y-2 text-slate-600">
                <li><a href="#" className="hover:text-slate-900 transition-colors">Facebook</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">Twitter</a></li>
                <li><a href="#" className="hover:text-slate-900 transition-colors">LinkedIn</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-slate-200 mt-12 pt-8 text-center text-slate-600">
            <p>&copy; 2025 VertexBuild. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
