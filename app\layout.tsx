import type { <PERSON>ada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { <PERSON><PERSON><PERSON>ider } from '@clerk/nextjs'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'VertexBuild - Launch your Nepali store in minutes',
  description: 'Build and launch your e-commerce store for the Nepali market with eSewa, Khalti payments and same-day delivery.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.variable}>{children}</body>
      </html>
    </ClerkProvider>
  )
}
