import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { items, customerInfo, paymentMethod } = body

    // Calculate total
    const total = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0)

    // Generate order number
    const orderNumber = `VB${Date.now()}`

    // Create order (mock for now)
    const order = {
      id: Date.now().toString(),
      orderNumber,
      status: 'PENDING',
      total,
      customerName: customerInfo.name,
      customerEmail: customerInfo.email,
      customerPhone: customerInfo.phone,
      shippingAddress: customerInfo.address,
      paymentMethod,
      paymentStatus: 'PENDING',
      items,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    return NextResponse.json(order, { status: 201 })
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Mock orders data
    const orders = [
      {
        id: '1',
        orderNumber: 'VB1001',
        status: 'DELIVERED',
        total: 2500,
        customerName: 'Ram Sharma',
        customerEmail: '<EMAIL>',
        customerPhone: '9841234567',
        shippingAddress: 'Kathmandu, Nepal',
        paymentMethod: 'ESEWA',
        paymentStatus: 'PAID',
        createdAt: new Date(Date.now() - 86400000).toISOString(),
        updatedAt: new Date(Date.now() - 86400000).toISOString(),
      },
      {
        id: '2',
        orderNumber: 'VB1002',
        status: 'PROCESSING',
        total: 800,
        customerName: 'Sita Rai',
        customerEmail: '<EMAIL>',
        customerPhone: '9851234567',
        shippingAddress: 'Pokhara, Nepal',
        paymentMethod: 'KHALTI',
        paymentStatus: 'PAID',
        createdAt: new Date(Date.now() - 43200000).toISOString(),
        updatedAt: new Date(Date.now() - 43200000).toISOString(),
      },
      {
        id: '3',
        orderNumber: 'VB1003',
        status: 'PENDING',
        total: 4500,
        customerName: 'Hari Thapa',
        customerEmail: '<EMAIL>',
        customerPhone: '9861234567',
        shippingAddress: 'Lalitpur, Nepal',
        paymentMethod: 'COD',
        paymentStatus: 'PENDING',
        createdAt: new Date(Date.now() - 21600000).toISOString(),
        updatedAt: new Date(Date.now() - 21600000).toISOString(),
      },
    ]

    return NextResponse.json(orders)
  } catch (error) {
    console.error('Error fetching orders:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
