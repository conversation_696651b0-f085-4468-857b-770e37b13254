import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // For now, return dummy data since we don't have tenant setup yet
    const dummyProducts = [
      {
        id: '1',
        name: 'Traditional Nepali Kurta',
        description: 'Handwoven traditional kurta made from local cotton',
        price: 2500,
        stock: 15,
        category: 'Clothing',
        isActive: true,
        imageUrl: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'Himalayan Honey',
        description: 'Pure honey from the Himalayan region',
        price: 800,
        stock: 25,
        category: 'Food',
        isActive: true,
        imageUrl: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '3',
        name: 'Handmade Pashmina Shawl',
        description: 'Authentic pashmina shawl from local artisans',
        price: 4500,
        stock: 8,
        category: 'Accessories',
        isActive: true,
        imageUrl: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]

    return NextResponse.json(dummyProducts)
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, price, stock, category, imageUrl } = body

    // Validate required fields
    if (!name || !price) {
      return NextResponse.json({ error: 'Name and price are required' }, { status: 400 })
    }

    // For now, return a mock response
    const newProduct = {
      id: Date.now().toString(),
      name,
      description: description || '',
      price: parseFloat(price),
      stock: parseInt(stock) || 0,
      category: category || 'Uncategorized',
      isActive: true,
      imageUrl: imageUrl || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    return NextResponse.json(newProduct, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
