import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { amount, orderId, customerInfo } = body

    // eSewa sandbox configuration
    const esewaConfig = {
      merchantId: process.env.ESEWA_MERCHANT_ID || 'EPAYTEST',
      secretKey: process.env.ESEWA_SECRET_KEY || '8gBm/:&EnhH.1/q',
      successUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/esewa/success`,
      failureUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/esewa/failure`,
    }

    // Generate signature for eSewa
    const message = `total_amount=${amount},transaction_uuid=${orderId},product_code=${esewaConfig.merchantId}`
    const signature = crypto
      .createHmac('sha256', esewaConfig.secretKey)
      .update(message)
      .digest('base64')

    const paymentData = {
      amount: amount,
      failure_url: esewaConfig.failureUrl,
      product_delivery_charge: '0',
      product_service_charge: '0',
      product_code: esewaConfig.merchantId,
      signature: signature,
      signed_field_names: 'total_amount,transaction_uuid,product_code',
      success_url: esewaConfig.successUrl,
      tax_amount: '0',
      total_amount: amount,
      transaction_uuid: orderId,
    }

    return NextResponse.json({
      paymentUrl: 'https://rc-epay.esewa.com.np/api/epay/main/v2/form',
      paymentData,
    })
  } catch (error) {
    console.error('eSewa payment error:', error)
    return NextResponse.json({ error: 'Payment initialization failed' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('transaction_uuid')
    const status = searchParams.get('status')

    if (status === 'COMPLETE') {
      // Update order status to paid
      // For now, just return success
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?orderId=${orderId}`)
    } else {
      return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/failed?orderId=${orderId}`)
    }
  } catch (error) {
    console.error('eSewa callback error:', error)
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_APP_URL}/checkout/failed`)
  }
}
