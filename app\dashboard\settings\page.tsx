'use client'

import { useState } from 'react'
import { Save, Store, CreditCard, MessageSquare } from 'lucide-react'

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    storeName: 'My Nepali Store',
    storeDescription: 'Quality products from Nepal',
    logoUrl: '',
    primaryColor: '#2563eb',
    esewaEnabled: true,
    esewaSecretKey: '',
    khaltiEnabled: true,
    khaltiSecretKey: '',
    smsEnabled: false,
    smsApiKey: '',
  })

  const [activeTab, setActiveTab] = useState('store')

  const handleSave = () => {
    // In a real app, this would save to the database
    alert('Settings saved successfully!')
  }

  const tabs = [
    { id: 'store', name: 'Store Settings', icon: Store },
    { id: 'payments', name: 'Payment Settings', icon: CreditCard },
    { id: 'notifications', name: 'SMS Settings', icon: MessageSquare },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-slate-900">Settings</h1>
        <p className="text-slate-600">Manage your store configuration</p>
      </div>

      <div className="bg-white rounded-2xl shadow-lg/10 border border-slate-200 overflow-hidden">
        {/* Tabs */}
        <div className="border-b border-slate-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-brand-500 text-brand-600'
                    : 'border-transparent text-slate-500 hover:text-slate-700'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'store' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-slate-900">Store Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Store Name
                  </label>
                  <input
                    type="text"
                    value={settings.storeName}
                    onChange={(e) => setSettings({ ...settings, storeName: e.target.value })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Primary Color
                  </label>
                  <input
                    type="color"
                    value={settings.primaryColor}
                    onChange={(e) => setSettings({ ...settings, primaryColor: e.target.value })}
                    className="w-full h-10 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Store Description
                </label>
                <textarea
                  value={settings.storeDescription}
                  onChange={(e) => setSettings({ ...settings, storeDescription: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Logo URL
                </label>
                <input
                  type="url"
                  value={settings.logoUrl}
                  onChange={(e) => setSettings({ ...settings, logoUrl: e.target.value })}
                  placeholder="https://example.com/logo.png"
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                />
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-slate-900">Payment Gateway Settings</h3>
              
              {/* eSewa Settings */}
              <div className="border border-slate-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="font-medium text-slate-900">eSewa</h4>
                    <p className="text-sm text-slate-600">Accept payments through eSewa</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.esewaEnabled}
                      onChange={(e) => setSettings({ ...settings, esewaEnabled: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                  </label>
                </div>
                {settings.esewaEnabled && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      eSewa Secret Key
                    </label>
                    <input
                      type="password"
                      value={settings.esewaSecretKey}
                      onChange={(e) => setSettings({ ...settings, esewaSecretKey: e.target.value })}
                      placeholder="Enter your eSewa secret key"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                    />
                  </div>
                )}
              </div>

              {/* Khalti Settings */}
              <div className="border border-slate-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="font-medium text-slate-900">Khalti</h4>
                    <p className="text-sm text-slate-600">Accept payments through Khalti</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.khaltiEnabled}
                      onChange={(e) => setSettings({ ...settings, khaltiEnabled: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                  </label>
                </div>
                {settings.khaltiEnabled && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Khalti Secret Key
                    </label>
                    <input
                      type="password"
                      value={settings.khaltiSecretKey}
                      onChange={(e) => setSettings({ ...settings, khaltiSecretKey: e.target.value })}
                      placeholder="Enter your Khalti secret key"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-slate-900">SMS Notification Settings</h3>
              
              <div className="border border-slate-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="font-medium text-slate-900">SMS Notifications</h4>
                    <p className="text-sm text-slate-600">Send order updates via SMS</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={settings.smsEnabled}
                      onChange={(e) => setSettings({ ...settings, smsEnabled: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-slate-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand-600"></div>
                  </label>
                </div>
                {settings.smsEnabled && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Sparrow SMS API Key
                    </label>
                    <input
                      type="password"
                      value={settings.smsApiKey}
                      onChange={(e) => setSettings({ ...settings, smsApiKey: e.target.value })}
                      placeholder="Enter your Sparrow SMS API key"
                      className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end pt-6 border-t border-slate-200">
            <button
              onClick={handleSave}
              className="flex items-center px-6 py-2 gradient-brand text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
            >
              <Save className="w-4 h-4 mr-2" />
              Save Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
