// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id          String   @id @default(cuid())
  name        String
  domain      String   @unique
  clerkOrgId  String?  @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users       User[]
  products    Product[]
  orders      Order[]
  settings    TenantSettings?

  @@map("tenants")
}

model User {
  id          String   @id @default(cuid())
  clerkUserId String   @unique
  email       String
  firstName   String?
  lastName    String?
  role        UserRole @default(MERCHANT)
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orders      Order[]

  @@map("users")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  price       Decimal  @db.Decimal(10, 2)
  imageUrl    String?
  category    String?
  stock       Int      @default(0)
  isActive    Boolean  @default(true)
  tenantId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant      Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  orderItems  OrderItem[]

  @@map("products")
}

model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)
  total       Decimal     @db.Decimal(10, 2)

  // Customer info
  customerName    String
  customerEmail   String
  customerPhone   String
  shippingAddress String

  // Payment
  paymentMethod   PaymentMethod?
  paymentStatus   PaymentStatus @default(PENDING)
  paymentId       String?

  tenantId    String
  userId      String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User?       @relation(fields: [userId], references: [id])
  orderItems  OrderItem[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  price     Decimal @db.Decimal(10, 2)

  orderId   String
  productId String

  // Relations
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product   Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model TenantSettings {
  id                String  @id @default(cuid())
  storeName         String
  storeDescription  String?
  logoUrl           String?
  primaryColor      String  @default("#2563eb")

  // Payment settings
  esewaEnabled      Boolean @default(false)
  esewaSecretKey    String?
  khaltiEnabled     Boolean @default(false)
  khaltiSecretKey   String?

  // SMS settings
  smsEnabled        Boolean @default(false)
  smsApiKey         String?

  tenantId          String  @unique
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  tenant            Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@map("tenant_settings")
}

enum UserRole {
  ADMIN
  MERCHANT
  CUSTOMER
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

enum PaymentMethod {
  ESEWA
  KHALTI
  COD
}
