import { Package, ShoppingCart, DollarSign, TrendingUp } from 'lucide-react'

const stats = [
  {
    name: 'Total Products',
    value: '12',
    icon: Package,
    change: '+2 this week',
    changeType: 'positive',
  },
  {
    name: 'Total Orders',
    value: '48',
    icon: ShoppingCart,
    change: '+12% from last month',
    changeType: 'positive',
  },
  {
    name: 'Revenue',
    value: 'Rs 24,500',
    icon: DollarSign,
    change: '+8% from last month',
    changeType: 'positive',
  },
  {
    name: 'Growth',
    value: '+12.5%',
    icon: TrendingUp,
    change: '+2.1% from last month',
    changeType: 'positive',
  },
]

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-slate-900">Dashboard</h1>
        <p className="text-slate-600">Welcome back! Here's what's happening with your store.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white rounded-2xl p-6 shadow-lg/10 border border-slate-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600">{stat.name}</p>
                <p className="text-2xl font-bold text-slate-900 mt-1">{stat.value}</p>
              </div>
              <div className="w-12 h-12 bg-brand-50 rounded-xl flex items-center justify-center">
                <stat.icon className="w-6 h-6 text-brand-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-green-600 font-medium">{stat.change}</span>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-lg/10 border border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Recent Orders</h3>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between py-3 border-b border-slate-100 last:border-0">
                <div>
                  <p className="font-medium text-slate-900">Order #{1000 + i}</p>
                  <p className="text-sm text-slate-600">Customer Name</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-slate-900">Rs {(Math.random() * 5000 + 1000).toFixed(0)}</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Paid
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg/10 border border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Top Products</h3>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between py-3 border-b border-slate-100 last:border-0">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-slate-200 rounded-lg"></div>
                  <div>
                    <p className="font-medium text-slate-900">Product {i}</p>
                    <p className="text-sm text-slate-600">{Math.floor(Math.random() * 50 + 10)} sold</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-slate-900">Rs {(Math.random() * 2000 + 500).toFixed(0)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
