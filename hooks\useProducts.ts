import useSWR from 'swr'

const fetcher = (url: string) => fetch(url).then((res) => res.json())

export interface Product {
  id: string
  name: string
  description: string
  price: number
  stock: number
  category: string
  isActive: boolean
  imageUrl: string | null
  createdAt: string
  updatedAt: string
}

export function useProducts() {
  const { data, error, isLoading, mutate } = useSWR<Product[]>('/api/products', fetcher)

  return {
    products: data || [],
    isLoading,
    isError: error,
    mutate,
  }
}

export async function createProduct(productData: Partial<Product>) {
  const response = await fetch('/api/products', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(productData),
  })

  if (!response.ok) {
    throw new Error('Failed to create product')
  }

  return response.json()
}

export async function updateProduct(id: string, productData: Partial<Product>) {
  const response = await fetch(`/api/products/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(productData),
  })

  if (!response.ok) {
    throw new Error('Failed to update product')
  }

  return response.json()
}

export async function deleteProduct(id: string) {
  const response = await fetch(`/api/products/${id}`, {
    method: 'DELETE',
  })

  if (!response.ok) {
    throw new Error('Failed to delete product')
  }

  return response.json()
}
